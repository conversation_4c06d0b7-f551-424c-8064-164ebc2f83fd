<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'email_verified_at',
        'password',
        'is_admin',
        'role',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'paddle_customer_id',
        'coinbase_commerce_customer_id',
        'subscription_plan',
        'subscription_status',
        'subscription_ends_at',
        'search_count',
        'daily_reset',
        'status',
        'approval_status',
        'approved_by',
        'approved_at',
        'suspended_at',
        'suspended_by',
        'suspension_reason',
        'suspension_expires_at',
        'last_login_at',
        'login_count',
        'two_factor_enabled',
        'two_factor_confirmed_at',
        'current_otp_code',
        'otp_expires_at',
        'otp_attempts',
        'otp_last_attempt_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'daily_reset' => 'date',
            'subscription_ends_at' => 'datetime',
            'approved_at' => 'datetime',
            'suspended_at' => 'datetime',
            'suspension_expires_at' => 'datetime',
            'last_login_at' => 'datetime',
            'two_factor_enabled' => 'boolean',
            'two_factor_confirmed_at' => 'datetime',
            'otp_expires_at' => 'datetime',
            'otp_last_attempt_at' => 'datetime',
            'is_admin' => 'boolean',
        ];
    }

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // Automatically set role when is_admin is changed
        static::saving(function (User $user) {
            if ($user->isDirty('is_admin')) {
                if ($user->is_admin) {
                    $user->role = 'admin';
                } elseif ($user->role === 'admin') {
                    // Only change role if it was admin and is_admin is being set to false
                    $user->role = 'user';
                }
            }
        });
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->where('status', 'active')->with('pricingPlan');
    }

    /**
     * Get the user's search history.
     */
    public function searches(): HasMany
    {
        return $this->hasMany(UserSearch::class);
    }

    /**
     * Get the user's favorites.
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(UserFavorite::class);
    }

    /**
     * Get the user's payment requests.
     */
    public function paymentRequests(): HasMany
    {
        return $this->hasMany(PaymentRequest::class);
    }

    /**
     * Get the user's activity logs.
     */
    public function activityLogs(): HasMany
    {
        return $this->hasMany(UserActivityLog::class);
    }

    /**
     * Get the user's notifications.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(UserNotification::class);
    }

    /**
     * Get the user's Paddle transactions.
     */
    public function paddleTransactions(): HasMany
    {
        return $this->hasMany(PaddleTransaction::class);
    }

    /**
     * Get the user's Coinbase Commerce transactions.
     */
    public function coinbaseCommerceTransactions(): HasMany
    {
        return $this->hasMany(CoinbaseCommerceTransaction::class);
    }

    /**
     * Get the admin who approved this user.
     */
    public function approvedBy(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the admin who suspended this user.
     */
    public function suspendedBy(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'suspended_by');
    }

    /**
     * Get impersonation logs where this user was the admin.
     */
    public function impersonationLogsAsAdmin(): HasMany
    {
        return $this->hasMany(UserImpersonationLog::class, 'admin_user_id');
    }

    /**
     * Get impersonation logs where this user was the target.
     */
    public function impersonationLogsAsTarget(): HasMany
    {
        return $this->hasMany(UserImpersonationLog::class, 'target_user_id');
    }

    /**
     * Get the user's contact submissions.
     */
    public function contactSubmissions(): HasMany
    {
        return $this->hasMany(\App\Models\ContactSubmission::class);
    }

    /**
     * Check if user has premium subscription.
     */
    public function isPremium(): bool
    {
        if ($this->subscription_plan !== 'premium') {
            return false;
        }

        $activeSubscription = $this->activeSubscription;
        return $activeSubscription && $activeSubscription->isActive();
    }

    /**
     * Check if user has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        $activeSubscription = $this->activeSubscription;
        return $activeSubscription && $activeSubscription->isActive();
    }

    /**
     * Check if user can perform search (within daily limit).
     */
    public function canSearch(): bool
    {
        $subscriptionService = app(\App\Services\SubscriptionService::class);
        return $subscriptionService->canUserSearch($this);
    }

    /**
     * Increment search count.
     */
    public function incrementSearchCount(): void
    {
        // Always increment search count for tracking purposes, except for admin users
        if (!$this->isAdmin()) {
            $this->increment('search_count');
        }
    }

    /**
     * Get user's remaining searches for today.
     */
    public function getRemainingSearches(): int
    {
        $subscriptionService = app(\App\Services\SubscriptionService::class);
        return $subscriptionService->getRemainingSearches($this);
    }

    /**
     * Get the user's role.
     */
    public function getRole(): string
    {
        // If role field exists, use it
        if (isset($this->attributes['role'])) {
            return $this->role;
        }

        // Fallback based on is_admin field for backward compatibility
        if (isset($this->attributes['is_admin']) && $this->is_admin) {
            return 'admin';
        }

        return 'user';
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->getRole() === $role;
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        // Check role field first if it exists and has a value
        if (!empty($this->role)) {
            return $this->role === 'admin';
        }

        // Fallback to is_admin field
        if (!is_null($this->is_admin)) {
            return (bool) $this->is_admin;
        }

        // Fallback to email-based check for backward compatibility
        $adminEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        return in_array($this->email, $adminEmails);
    }

    /**
     * Check if user is a content manager.
     */
    public function isContentManager(): bool
    {
        return $this->hasRole('content_manager');
    }

    /**
     * Check if user can manage content (admin or content manager).
     */
    public function canManageContent(): bool
    {
        return $this->isAdmin() || $this->isContentManager();
    }

    /**
     * Check if user can manage users (admin only).
     */
    public function canManageUsers(): bool
    {
        return $this->isAdmin();
    }

    /**
     * Check if user can access admin dashboard.
     */
    public function canAccessAdminDashboard(): bool
    {
        return $this->canManageContent();
    }

    /**
     * Check if user is active and can access the system.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->approval_status === 'approved';
    }

    /**
     * Check if user is suspended.
     */
    public function isSuspended(): bool
    {
        if ($this->status !== 'suspended') {
            return false;
        }

        // Check if temporary suspension has expired
        if ($this->suspension_expires_at && $this->suspension_expires_at->isPast()) {
            $this->update([
                'status' => 'active',
                'suspended_at' => null,
                'suspended_by' => null,
                'suspension_reason' => null,
                'suspension_expires_at' => null,
            ]);
            return false;
        }

        return true;
    }

    /**
     * Check if user is pending approval.
     */
    public function isPendingApproval(): bool
    {
        return $this->approval_status === 'pending';
    }

    /**
     * Approve the user.
     */
    public function approve(User $admin): void
    {
        $this->update([
            'approval_status' => 'approved',
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'status' => 'active',
        ]);
    }

    /**
     * Suspend the user.
     */
    public function suspend(User $admin, string $reason, ?\Carbon\Carbon $expiresAt = null): void
    {
        $this->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspended_by' => $admin->id,
            'suspension_reason' => $reason,
            'suspension_expires_at' => $expiresAt,
        ]);
    }

    /**
     * Unsuspend the user.
     */
    public function unsuspend(): void
    {
        $this->update([
            'status' => 'active',
            'suspended_at' => null,
            'suspended_by' => null,
            'suspension_reason' => null,
            'suspension_expires_at' => null,
        ]);
    }

    /**
     * Log user activity.
     */
    public function logActivity(string $activityType, string $description, array $metadata = [], ?User $performedBy = null): void
    {
        $this->activityLogs()->create([
            'activity_type' => $activityType,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => $metadata,
            'performed_by' => $performedBy?->id,
        ]);
    }

    /**
     * Update last login information.
     */
    public function updateLastLogin(): void
    {
        $this->update([
            'last_login_at' => now(),
            'login_count' => $this->login_count + 1,
        ]);
    }


}

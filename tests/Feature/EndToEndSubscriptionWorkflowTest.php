<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\Part;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Tests\TestCase;

class EndToEndSubscriptionWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->subscriptionService = app(SubscriptionService::class);
        
        // Create pricing plans
        $this->createPricingPlans();
        
        // Create test data for search functionality
        $this->createTestSearchData();
    }

    private function createPricingPlans(): void
    {
        PricingPlan::truncate();

        PricingPlan::factory()->create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'search_limit' => 20,
            'is_active' => true,
            'is_default' => true,
        ]);

        PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19,
            'search_limit' => -1,
            'is_active' => true,
            'offline_payment_enabled' => true,
        ]);
    }

    private function createTestSearchData(): void
    {
        $category = Category::factory()->create(['name' => 'Screen']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $model = MobileModel::factory()->create([
            'name' => 'iPhone 12',
            'brand_id' => $brand->id,
        ]);
        
        // Create multiple parts for testing
        for ($i = 1; $i <= 25; $i++) {
            Part::factory()->create([
                'name' => "iPhone 12 Screen Part {$i}",
                'category_id' => $category->id,
                'is_active' => true,
            ]);
        }
    }

    /** @test */
    public function complete_user_journey_from_registration_to_premium_subscription()
    {
        Event::fake();
        Mail::fake();

        // Step 1: User visits registration page
        $response = $this->get('/register');
        $response->assertStatus(200);

        // Step 2: User registers
        $response = $this->postWithCsrf('/register', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertRedirect(route('verification.notice'));
        $this->assertAuthenticated();

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertEquals('free', $user->subscription_plan);
        $this->assertEquals(0, $user->search_count);

        // Step 3: User verifies email
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $response = $this->get($verificationUrl);
        $response->assertRedirect(route('dashboard', absolute: false) . '?verified=1');

        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertEquals('active', $user->status);

        // Step 4: User accesses dashboard
        $response = $this->get('/dashboard');
        $response->assertStatus(200);

        // Step 5: User performs searches within free limit
        for ($i = 1; $i <= 15; $i++) {
            $response = $this->get("/search/results?q=iPhone+{$i}");
            $response->assertStatus(200);
        }

        $user->refresh();
        $this->assertEquals(15, $user->search_count);
        $this->assertEquals(5, $user->getRemainingSearches());

        // Step 6: User approaches search limit
        for ($i = 16; $i <= 20; $i++) {
            $response = $this->get("/search/results?q=iPhone+{$i}");
            $response->assertStatus(200);
        }

        $user->refresh();
        $this->assertEquals(20, $user->search_count);
        $this->assertEquals(0, $user->getRemainingSearches());

        // Step 7: User hits search limit
        $response = $this->get('/search/results?q=blocked');
        $response->assertStatus(302);
        $response->assertRedirect(route('subscription.plans'));
        $response->assertSessionHas('error', 'Daily search limit exceeded');

        // Step 8: User views subscription plans
        $response = $this->get(route('subscription.plans'));
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('subscription/plans')
                ->has('plans')
                ->where('user.subscription_plan', 'free')
        );

        // Step 9: User upgrades to premium via offline payment
        $response = $this->post(route('subscription.checkout'), [
            'payment_gateway' => 'offline',
        ]);

        $response->assertRedirect(route('subscription.dashboard'));
        $response->assertSessionHas('success', 'Premium subscription activated successfully!');

        // Step 10: Verify subscription was created
        $user->refresh();
        $this->assertEquals('premium', $user->subscription_plan);
        $this->assertTrue($user->isPremium());
        $this->assertEquals(-1, $user->getRemainingSearches());

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $user->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'payment_gateway' => 'offline',
        ]);

        // Step 11: User can now search unlimited
        $response = $this->get('/search/results?q=unlimited');
        $response->assertStatus(200);

        // Step 12: User views subscription dashboard
        $response = $this->get(route('subscription.dashboard'));
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('subscription/dashboard')
                ->where('user.subscription_plan', 'premium')
                ->has('subscription')
        );

        // Step 13: Perform multiple searches to verify unlimited access
        for ($i = 1; $i <= 10; $i++) {
            $response = $this->get("/search/results?q=premium+search+{$i}");
            $response->assertStatus(200);
        }

        $user->refresh();
        $this->assertTrue($user->canSearch());
        $this->assertEquals(-1, $user->getRemainingSearches());
    }

    /** @test */
    public function user_journey_with_daily_limit_reset()
    {
        Event::fake();
        Mail::fake();

        // Register and verify user
        $this->postWithCsrf('/register', [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );
        $this->get($verificationUrl);

        // Use up all searches
        for ($i = 1; $i <= 20; $i++) {
            $response = $this->get("/search/results?q=search+{$i}");
            $response->assertStatus(200);
        }

        $user->refresh();
        $this->assertEquals(20, $user->search_count);
        $this->assertFalse($user->canSearch());

        // Verify search is blocked
        $response = $this->get('/search/results?q=blocked');
        $response->assertStatus(302);
        $response->assertRedirect(route('subscription.plans'));

        // Simulate next day by updating daily_reset
        $user->update(['daily_reset' => yesterday()->toDateString()]);

        // User should be able to search again
        $response = $this->get('/search/results?q=new+day');
        $response->assertStatus(200);

        // Verify reset occurred
        $user->refresh();
        $this->assertEquals(1, $user->search_count);
        $this->assertEquals(today()->toDateString(), $user->daily_reset);
        $this->assertEquals(19, $user->getRemainingSearches());
    }

    /** @test */
    public function admin_user_complete_workflow()
    {
        Event::fake();
        Mail::fake();

        // Register admin user
        $response = $this->postWithCsrf('/register', [
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $admin = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($admin->isAdmin());

        // Verify email
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $admin->id, 'hash' => sha1($admin->email)]
        );

        $response = $this->get($verificationUrl);
        $response->assertRedirect(route('admin.dashboard', absolute: false) . '?verified=1');

        // Admin should have unlimited search access
        $this->assertTrue($admin->canSearch());
        $this->assertEquals(-1, $admin->getRemainingSearches());

        // Perform many searches
        for ($i = 1; $i <= 50; $i++) {
            $response = $this->get("/search/results?q=admin+search+{$i}");
            $response->assertStatus(200);
        }

        // Search count should not increment for admin
        $admin->refresh();
        $this->assertEquals(0, $admin->search_count);
        $this->assertTrue($admin->canSearch());
    }

    /** @test */
    public function subscription_cancellation_and_reactivation_workflow()
    {
        Event::fake();
        Mail::fake();

        // Register and verify user
        $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );
        $this->get($verificationUrl);

        // Upgrade to premium
        $response = $this->post(route('subscription.checkout'), [
            'payment_gateway' => 'offline',
        ]);

        $user->refresh();
        $this->assertTrue($user->isPremium());

        // Use premium features (unlimited search)
        for ($i = 1; $i <= 30; $i++) {
            $response = $this->get("/search/results?q=premium+{$i}");
            $response->assertStatus(200);
        }

        $user->refresh();
        $this->assertTrue($user->canSearch());

        // Cancel subscription
        $result = $this->subscriptionService->cancelSubscription($user);
        $this->assertTrue($result);

        $user->refresh();
        $this->assertEquals('free', $user->subscription_plan);
        $this->assertFalse($user->isPremium());

        // User should now be limited again (and blocked due to high search count)
        $this->assertFalse($user->canSearch());

        // Reset search count to simulate new day
        $user->update([
            'search_count' => 0,
            'daily_reset' => today()->toDateString(),
        ]);

        // User should be able to search within free limits again
        $this->assertTrue($user->canSearch());
        $this->assertEquals(20, $user->getRemainingSearches());

        // Reactivate premium subscription
        $response = $this->post(route('subscription.checkout'), [
            'payment_gateway' => 'offline',
        ]);

        $user->refresh();
        $this->assertTrue($user->isPremium());
        $this->assertEquals(-1, $user->getRemainingSearches());
    }

    /** @test */
    public function guest_to_registered_user_workflow()
    {
        // Guest user tries to search (should be redirected to home)
        $response = $this->get('/search/results?q=guest+search');
        $response->assertStatus(302);
        $response->assertRedirect(route('home'));

        // Guest registers
        $response = $this->postWithCsrf('/register', [
            'name' => 'Former Guest',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $user = User::where('email', '<EMAIL>')->first();

        // Verify email
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );
        $this->get($verificationUrl);

        // Now user can search
        $response = $this->get('/search/results?q=registered+search');
        $response->assertStatus(200);

        $user->refresh();
        $this->assertEquals(1, $user->search_count);
    }
}
